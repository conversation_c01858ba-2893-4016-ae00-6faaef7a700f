using Microsoft.EntityFrameworkCore;
using CoffeeShopWebsite.Models;

namespace CoffeeShopWebsite.Data
{
    public class CoffeeShopContext : DbContext
    {
        public CoffeeShopContext(DbContextOptions<CoffeeShopContext> options) : base(options)
        {
        }

        public DbSet<Category> Categories { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
        public DbSet<CartItem> CartItems { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Category configuration
            modelBuilder.Entity<Category>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.HasIndex(e => e.Name).IsUnique();
            });

            // Product configuration
            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Description).HasMaxLength(1000);
                entity.Property(e => e.Price).HasColumnType("decimal(18,2)");
                
                entity.HasOne(p => p.Category)
                      .WithMany(c => c.Products)
                      .HasForeignKey(p => p.CategoryId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // Customer configuration
            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(150);
                entity.Property(e => e.PhoneNumber).IsRequired().HasMaxLength(15);
                entity.Property(e => e.Address).HasMaxLength(300);
                entity.HasIndex(e => e.Email).IsUnique();
            });

            // Order configuration
            modelBuilder.Entity<Order>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");
                entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.CustomerPhone).IsRequired().HasMaxLength(15);
                entity.Property(e => e.DeliveryAddress).HasMaxLength(300);
                entity.Property(e => e.Notes).HasMaxLength(500);
                entity.HasIndex(e => e.OrderNumber).IsUnique();
                
                entity.HasOne(o => o.Customer)
                      .WithMany(c => c.Orders)
                      .HasForeignKey(o => o.CustomerId)
                      .OnDelete(DeleteBehavior.SetNull);
            });

            // OrderItem configuration
            modelBuilder.Entity<OrderItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.UnitPrice).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Notes).HasMaxLength(200);
                
                entity.HasOne(oi => oi.Order)
                      .WithMany(o => o.OrderItems)
                      .HasForeignKey(oi => oi.OrderId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasOne(oi => oi.Product)
                      .WithMany(p => p.OrderItems)
                      .HasForeignKey(oi => oi.ProductId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            // CartItem configuration
            modelBuilder.Entity<CartItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Notes).HasMaxLength(200);
                entity.Property(e => e.SessionId).HasMaxLength(100);
                
                entity.HasOne(ci => ci.Customer)
                      .WithMany(c => c.CartItems)
                      .HasForeignKey(ci => ci.CustomerId)
                      .OnDelete(DeleteBehavior.Cascade);
                      
                entity.HasOne(ci => ci.Product)
                      .WithMany(p => p.CartItems)
                      .HasForeignKey(ci => ci.ProductId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed Categories
            modelBuilder.Entity<Category>().HasData(
                new Category { Id = 1, Name = "Cà phê", Description = "Các loại cà phê truyền thống và hiện đại", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=300&h=200&fit=crop" },
                new Category { Id = 2, Name = "Trà", Description = "Các loại trà và trà sữa thơm ngon", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop" },
                new Category { Id = 3, Name = "Nước ép", Description = "Nước ép trái cây tươi 100% tự nhiên", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1613478223719-2ab802602423?w=300&h=200&fit=crop" },
                new Category { Id = 4, Name = "Sinh tố", Description = "Sinh tố các loại bổ dưỡng", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1505252585461-04db1eb84625?w=300&h=200&fit=crop" },
                new Category { Id = 5, Name = "Đá xay", Description = "Các loại đồ uống đá xay mát lạnh", IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300&h=200&fit=crop" }
            );

            // Seed Products
            modelBuilder.Entity<Product>().HasData(
                // Cà phê
                new Product { Id = 1, Name = "Cà phê đen", Description = "Cà phê đen truyền thống, đậm đà hương vị Việt Nam", Price = 15000, StockQuantity = 100, CategoryId = 1, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300&h=300&fit=crop" },
                new Product { Id = 2, Name = "Cà phê sữa", Description = "Cà phê sữa đá thơm ngon, ngọt ngào", Price = 18000, StockQuantity = 100, CategoryId = 1, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=300&h=300&fit=crop" },
                new Product { Id = 3, Name = "Cappuccino", Description = "Cappuccino Ý với lớp foam mịn màng", Price = 35000, StockQuantity = 50, CategoryId = 1, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1572442388796-11668a67e53d?w=300&h=300&fit=crop" },
                new Product { Id = 4, Name = "Latte", Description = "Latte thơm ngon với nghệ thuật latte art", Price = 40000, StockQuantity = 50, CategoryId = 1, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1561047029-3000c68339ca?w=300&h=300&fit=crop" },

                // Trà
                new Product { Id = 5, Name = "Trà đào", Description = "Trà đào cam sả tươi mát, thơm ngon", Price = 25000, StockQuantity = 80, CategoryId = 2, IsActive = true, IsFeatured = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1556679343-c7306c1976bc?w=300&h=300&fit=crop" },
                new Product { Id = 6, Name = "Trà sữa trân châu", Description = "Trà sữa trân châu đường đen đậm đà", Price = 30000, StockQuantity = 60, CategoryId = 2, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1525385133512-2f3bdd039054?w=300&h=300&fit=crop" },

                // Nước ép
                new Product { Id = 7, Name = "Nước ép cam", Description = "Nước ép cam tươi 100% không đường", Price = 20000, StockQuantity = 40, CategoryId = 3, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300&h=300&fit=crop" },
                new Product { Id = 8, Name = "Nước ép dưa hấu", Description = "Nước ép dưa hấu tươi mát, giải nhiệt", Price = 22000, StockQuantity = 30, CategoryId = 3, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1587049352846-4a222e784d38?w=300&h=300&fit=crop" },

                // Sinh tố
                new Product { Id = 9, Name = "Sinh tố bơ", Description = "Sinh tố bơ béo ngậy, bổ dưỡng", Price = 28000, StockQuantity = 35, CategoryId = 4, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1553530666-ba11a7da3888?w=300&h=300&fit=crop" },
                new Product { Id = 10, Name = "Sinh tố xoài", Description = "Sinh tố xoài ngọt mát, thơm ngon", Price = 26000, StockQuantity = 40, CategoryId = 4, IsActive = true, CreatedAt = new DateTime(2024, 1, 1), ImageUrl = "https://images.unsplash.com/photo-1546173159-315724a31696?w=300&h=300&fit=crop" }
            );
        }
    }
}

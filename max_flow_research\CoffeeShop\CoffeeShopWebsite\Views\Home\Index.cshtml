@{
    ViewData["Title"] = "Trang chủ";
    var featuredProducts = ViewBag.FeaturedProducts as List<CoffeeShopWebsite.Models.Product>;
    var categories = ViewBag.Categories as List<CoffeeShopWebsite.Models.Category>;
}

<!-- Hero Section -->
<div class="hero-section bg-primary text-white py-5 mb-5 rounded">
    <div class="container text-center">
        <h1 class="display-4 fw-bold mb-3">
            <i class="fas fa-coffee me-3"></i>Chào mừng đến Coffee Shop
        </h1>
        <p class="lead mb-4">Thưởng thức hương vị cà phê tuyệt vời và đồ uống giải khát tươi mát</p>
        <a asp-controller="Products" asp-action="Index" class="btn btn-light btn-lg">
            <i class="fas fa-shopping-bag me-2"></i>Xem sản phẩm
        </a>
    </div>
</div>

<!-- Categories Section -->
@if (categories != null && categories.Any())
{
    <div class="mb-5">
        <h2 class="text-center mb-4">
            <i class="fas fa-th-large me-2"></i>Danh mục sản phẩm
        </h2>
        <div class="row">
            @foreach (var category in categories)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm category-card">
                        @if (!string.IsNullOrEmpty(category.ImageUrl))
                        {
                            <img src="@category.ImageUrl" class="card-img-top" alt="@category.Name" style="height: 200px; object-fit: cover;">
                        }
                        else
                        {
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                <i class="fas fa-coffee fa-3x text-muted"></i>
                            </div>
                        }
                        <div class="card-body text-center">
                            <h5 class="card-title">@category.Name</h5>
                            <p class="card-text">@category.Description</p>
                            <a asp-controller="Products" asp-action="Category" asp-route-id="@category.Id"
                               class="btn btn-outline-primary">
                                <i class="fas fa-eye me-1"></i>Xem sản phẩm
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}

<!-- Featured Products Section -->
@if (featuredProducts != null && featuredProducts.Any())
{
    <div class="mb-5">
        <h2 class="text-center mb-4">
            <i class="fas fa-star me-2"></i>Sản phẩm nổi bật
        </h2>
        <div class="row">
            @foreach (var product in featuredProducts)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm product-card">
                        @if (!string.IsNullOrEmpty(product.ImageUrl))
                        {
                            <div style="height: 200px; overflow: hidden;">
                                <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover; width: 100%;">
                            </div>
                        }
                        else
                        {
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                <i class="fas fa-coffee fa-3x text-muted"></i>
                            </div>
                        }
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">@product.Name</h5>
                            <p class="card-text flex-grow-1">@product.Description</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h5 price-tag mb-0">@product.Price.ToString("N0") VNĐ</span>
                                <div>
                                    <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id"
                                       class="btn btn-outline-primary btn-sm me-1" title="Xem chi tiết">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-primary btn-sm add-to-cart" data-product-id="@product.Id" title="Thêm vào giỏ hàng">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
        <div class="text-center">
            <a asp-controller="Products" asp-action="Featured" class="btn btn-primary">
                <i class="fas fa-star me-2"></i>Xem tất cả sản phẩm nổi bật
            </a>
        </div>
    </div>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Load cart count on page load
            updateCartCount();

            // Add to cart functionality
            $('.add-to-cart').click(function() {
                var productId = $(this).data('product-id');
                var button = $(this);

                // Disable button during request
                button.prop('disabled', true);
                var originalHtml = button.html();
                button.html('<i class="fas fa-spinner fa-spin"></i>');

                $.ajax({
                    url: '@Url.Action("AddToCart", "Products")',
                    type: 'POST',
                    data: {
                        productId: productId,
                        quantity: 1
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            showToast('success', response.message);

                            // Update cart count
                            updateCartCount();
                        } else {
                            showToast('error', response.message);
                        }
                    },
                    error: function() {
                        showToast('error', 'Có lỗi xảy ra. Vui lòng thử lại.');
                    },
                    complete: function() {
                        // Re-enable button
                        button.prop('disabled', false);
                        button.html(originalHtml);
                    }
                });
            });
        });

        function updateCartCount() {
            // This would typically make an AJAX call to get cart count
            // For now, we'll implement it when we create the cart functionality
        }

        function showToast(type, message) {
            // Simple toast notification
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var toast = $('<div class="alert ' + alertClass + ' alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 9999;">' +
                         '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                         message + '</div>');

            $('body').append(toast);

            // Auto dismiss after 3 seconds
            setTimeout(function() {
                toast.alert('close');
            }, 3000);
        }
    </script>
}

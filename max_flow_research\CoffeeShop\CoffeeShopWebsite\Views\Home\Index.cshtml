﻿@{
    ViewData["Title"] = "Trang chủ";
    var featuredProducts = ViewBag.FeaturedProducts as List<CoffeeShopWebsite.Models.Product>;
    var categories = ViewBag.Categories as List<CoffeeShopWebsite.Models.Category>;
}

<!-- Hero Section -->
<div class="hero-section bg-primary text-white py-5 mb-5 rounded">
    <div class="container text-center">
        <h1 class="display-4 fw-bold mb-3">
            <i class="fas fa-coffee me-3"></i>Chào mừng đến Coffee Shop
        </h1>
        <p class="lead mb-4">Thưởng thức hương vị cà phê tuyệt vời và đồ uống giải khát tươi mát</p>
        <a asp-controller="Products" asp-action="Index" class="btn btn-light btn-lg">
            <i class="fas fa-shopping-bag me-2"></i>Xem sản phẩm
        </a>
    </div>
</div>

<!-- Categories Section -->
@if (categories != null && categories.Any())
{
    <div class="mb-5">
        <h2 class="text-center mb-4">
            <i class="fas fa-th-large me-2"></i>Danh mục sản phẩm
        </h2>
        <div class="row">
            @foreach (var category in categories)
            {
                <div class="col-md-4 col-sm-6 mb-3">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-coffee fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">@category.Name</h5>
                            <p class="card-text">@category.Description</p>
                            <a asp-controller="Products" asp-action="Category" asp-route-id="@category.Id"
                               class="btn btn-outline-primary">
                                Xem sản phẩm
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}

<!-- Featured Products Section -->
@if (featuredProducts != null && featuredProducts.Any())
{
    <div class="mb-5">
        <h2 class="text-center mb-4">
            <i class="fas fa-star me-2"></i>Sản phẩm nổi bật
        </h2>
        <div class="row">
            @foreach (var product in featuredProducts)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        @if (!string.IsNullOrEmpty(product.ImageUrl))
                        {
                            <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 200px; object-fit: cover;">
                        }
                        else
                        {
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                <i class="fas fa-coffee fa-3x text-muted"></i>
                            </div>
                        }
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">@product.Name</h5>
                            <p class="card-text flex-grow-1">@product.Description</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h5 text-primary mb-0">@product.Price.ToString("N0") VNĐ</span>
                                <div>
                                    <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id"
                                       class="btn btn-outline-primary btn-sm me-1">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-primary btn-sm add-to-cart" data-product-id="@product.Id">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
        <div class="text-center">
            <a asp-controller="Products" asp-action="Featured" class="btn btn-primary">
                <i class="fas fa-star me-2"></i>Xem tất cả sản phẩm nổi bật
            </a>
        </div>
    </div>
}

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CoffeeShopWebsite.Models
{
    public class CartItem
    {
        public int Id { get; set; }
        
        [Required(ErrorMessage = "Số lượng là bắt buộc")]
        [Range(1, int.MaxValue, ErrorMessage = "Số lượng phải lớn hơn 0")]
        public int Quantity { get; set; }
        
        public DateTime AddedAt { get; set; } = DateTime.Now;
        
        [StringLength(200, ErrorMessage = "<PERSON>hi chú không được vượt quá 200 ký tự")]
        public string? Notes { get; set; }
        
        // For session-based cart (guest users)
        public string? SessionId { get; set; }
        
        // Foreign keys
        public int? CustomerId { get; set; }
        
        [Required]
        public int ProductId { get; set; }
        
        // Navigation properties
        public virtual Customer? Customer { get; set; }
        public virtual Product Product { get; set; } = null!;
        
        // Calculated property
        [NotMapped]
        public decimal TotalPrice => Quantity * (Product?.Price ?? 0);
    }
}
